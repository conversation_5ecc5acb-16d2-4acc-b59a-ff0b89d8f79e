#pragma once

#include <vector>
#include <string>
#include <chrono>
#include <memory>
#include <imgui.h>

enum class NotificationType {
    INFO,
    SUCCESS,
    WARNING,
    ERROR
};

struct Notification {
    std::string message;
    NotificationType type;
    std::chrono::steady_clock::time_point creation_time;
    float duration_seconds;
    float fade_in_duration;
    float fade_out_duration;
    bool is_persistent;  // If true, notification won't auto-disappear
    
    // Animation state
    float alpha;
    float target_alpha;
    bool is_fading_out;
    
    Notification(const std::string& msg, NotificationType t, float duration = 3.0f, bool persistent = false)
        : message(msg)
        , type(t)
        , creation_time(std::chrono::steady_clock::now())
        , duration_seconds(duration)
        , fade_in_duration(0.3f)
        , fade_out_duration(0.5f)
        , is_persistent(persistent)
        , alpha(0.0f)
        , target_alpha(1.0f)
        , is_fading_out(false)
    {}
};

class NotificationSystem {
public:
    NotificationSystem();
    ~NotificationSystem();
    
    // Core functionality
    void Update();
    void Render();
    
    // Add notifications
    void AddNotification(const std::string& message, NotificationType type = NotificationType::INFO, float duration = 3.0f);
    void AddPersistentNotification(const std::string& message, NotificationType type = NotificationType::INFO);
    
    // Convenience methods for different types
    void ShowInfo(const std::string& message, float duration = 3.0f);
    void ShowSuccess(const std::string& message, float duration = 3.0f);
    void ShowWarning(const std::string& message, float duration = 4.0f);
    void ShowError(const std::string& message, float duration = 5.0f);
    
    // Management
    void ClearAll();
    void ClearType(NotificationType type);
    void RemoveNotification(size_t index);
    
    // Configuration
    void SetPosition(float x, float y);
    void SetMaxNotifications(size_t max_count);
    void SetDefaultDuration(float duration);
    void SetAnimationSpeed(float speed);
    
    // State queries
    size_t GetNotificationCount() const;
    bool HasNotifications() const;
    bool HasNotificationsOfType(NotificationType type) const;
    
private:
    std::vector<std::unique_ptr<Notification>> notifications_;
    
    // Configuration
    ImVec2 position_;
    float notification_width_;
    float notification_height_;
    float notification_spacing_;
    size_t max_notifications_;
    float default_duration_;
    float animation_speed_;
    
    // Styling
    ImVec4 GetTypeColor(NotificationType type) const;
    ImVec4 GetTypeBorderColor(NotificationType type) const;
    const char* GetTypeIcon(NotificationType type) const;
    
    // Animation helpers
    void UpdateNotificationAnimation(Notification& notification);
    float CalculateNotificationAlpha(const Notification& notification) const;
    
    // Rendering helpers
    void RenderNotification(const Notification& notification, float y_offset);
    ImVec2 CalculateNotificationPosition(float y_offset) const;
    
    // Management helpers
    void RemoveExpiredNotifications();
    void EnsureMaxNotificationLimit();
};

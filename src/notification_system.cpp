#include "notification_system.h"
#include <algorithm>
#include <cmath>

NotificationSystem::NotificationSystem()
    : position_(ImVec2(0.0f, 30.0f))  // Top-right, below menu bar
    , notification_width_(300.0f)
    , notification_height_(60.0f)
    , notification_spacing_(10.0f)
    , max_notifications_(5)
    , default_duration_(3.0f)
    , animation_speed_(1.0f)
{
}

NotificationSystem::~NotificationSystem() {
}

void NotificationSystem::Update() {
    // Update animations for all notifications
    for (auto& notification : notifications_) {
        UpdateNotificationAnimation(*notification);
    }
    
    // Remove expired notifications
    RemoveExpiredNotifications();
    
    // Ensure we don't exceed max notification limit
    EnsureMaxNotificationLimit();
}

void NotificationSystem::Render() {
    if (notifications_.empty()) {
        return;
    }

    // Debug output
    printf("Rendering %zu notifications\n", notifications_.size());

    // Get window size to position notifications at top-right
    ImGuiIO& io = ImGui::GetIO();
    float window_width = io.DisplaySize.x;

    // Update position to be at top-right
    position_.x = window_width - notification_width_ - 20.0f;  // 20px margin from right edge

    float current_y_offset = 0.0f;

    // Render notifications from top to bottom
    for (size_t i = 0; i < notifications_.size(); ++i) {
        const auto& notification = notifications_[i];
        printf("Notification %zu: alpha=%.3f, message='%s'\n", i, notification->alpha, notification->message.c_str());

        if (notification->alpha > 0.01f) {  // Only render visible notifications
            RenderNotification(*notification, current_y_offset);
            current_y_offset += notification_height_ + notification_spacing_;
        }
    }
}

void NotificationSystem::AddNotification(const std::string& message, NotificationType type, float duration) {
    auto notification = std::make_unique<Notification>(message, type, duration, false);
    notifications_.push_back(std::move(notification));

    // Debug output
    printf("Added notification: %s (count: %zu)\n", message.c_str(), notifications_.size());
}

void NotificationSystem::AddPersistentNotification(const std::string& message, NotificationType type) {
    auto notification = std::make_unique<Notification>(message, type, 0.0f, true);
    notifications_.push_back(std::move(notification));
}

void NotificationSystem::ShowInfo(const std::string& message, float duration) {
    AddNotification(message, NotificationType::INFO, duration);
}

void NotificationSystem::ShowSuccess(const std::string& message, float duration) {
    AddNotification(message, NotificationType::SUCCESS, duration);
}

void NotificationSystem::ShowWarning(const std::string& message, float duration) {
    AddNotification(message, NotificationType::WARNING, duration);
}

void NotificationSystem::ShowError(const std::string& message, float duration) {
    AddNotification(message, NotificationType::ERROR, duration);
}

void NotificationSystem::ClearAll() {
    notifications_.clear();
}

void NotificationSystem::ClearType(NotificationType type) {
    notifications_.erase(
        std::remove_if(notifications_.begin(), notifications_.end(),
            [type](const std::unique_ptr<Notification>& n) {
                return n->type == type;
            }),
        notifications_.end()
    );
}

void NotificationSystem::RemoveNotification(size_t index) {
    if (index < notifications_.size()) {
        notifications_.erase(notifications_.begin() + index);
    }
}

void NotificationSystem::SetPosition(float x, float y) {
    position_ = ImVec2(x, y);
}

void NotificationSystem::SetMaxNotifications(size_t max_count) {
    max_notifications_ = max_count;
}

void NotificationSystem::SetDefaultDuration(float duration) {
    default_duration_ = duration;
}

void NotificationSystem::SetAnimationSpeed(float speed) {
    animation_speed_ = speed;
}

size_t NotificationSystem::GetNotificationCount() const {
    return notifications_.size();
}

bool NotificationSystem::HasNotifications() const {
    return !notifications_.empty();
}

bool NotificationSystem::HasNotificationsOfType(NotificationType type) const {
    return std::any_of(notifications_.begin(), notifications_.end(),
        [type](const std::unique_ptr<Notification>& n) {
            return n->type == type;
        });
}

ImVec4 NotificationSystem::GetTypeColor(NotificationType type) const {
    switch (type) {
        case NotificationType::INFO:
            return ImVec4(0.2f, 0.6f, 1.0f, 0.9f);  // Blue
        case NotificationType::SUCCESS:
            return ImVec4(0.2f, 0.8f, 0.2f, 0.9f);  // Green
        case NotificationType::WARNING:
            return ImVec4(1.0f, 0.8f, 0.2f, 0.9f);  // Orange
        case NotificationType::ERROR:
            return ImVec4(1.0f, 0.3f, 0.3f, 0.9f);  // Red
        default:
            return ImVec4(0.5f, 0.5f, 0.5f, 0.9f);  // Gray
    }
}

ImVec4 NotificationSystem::GetTypeBorderColor(NotificationType type) const {
    ImVec4 base_color = GetTypeColor(type);
    return ImVec4(base_color.x * 0.8f, base_color.y * 0.8f, base_color.z * 0.8f, base_color.w);
}

const char* NotificationSystem::GetTypeIcon(NotificationType type) const {
    switch (type) {
        case NotificationType::INFO:
            return "ℹ";
        case NotificationType::SUCCESS:
            return "✓";
        case NotificationType::WARNING:
            return "⚠";
        case NotificationType::ERROR:
            return "✗";
        default:
            return "•";
    }
}

void NotificationSystem::UpdateNotificationAnimation(Notification& notification) {
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - notification.creation_time).count();
    float elapsed_seconds = elapsed / 1000.0f;

    // Debug output (only for first few frames to avoid spam)
    static int debug_counter = 0;
    if (debug_counter < 10) {
        printf("Animation update: elapsed=%.3fs, alpha=%.3f, target_alpha=%.3f\n",
               elapsed_seconds, notification.alpha, notification.target_alpha);
        debug_counter++;
    }

    // Check if notification should start fading out
    if (!notification.is_persistent && !notification.is_fading_out &&
        elapsed_seconds >= (notification.duration_seconds - notification.fade_out_duration)) {
        notification.is_fading_out = true;
        notification.target_alpha = 0.0f;
        printf("Starting fade out for notification\n");
    }

    // Calculate target alpha based on animation phase
    if (!notification.is_fading_out) {
        // Fade in phase
        if (elapsed_seconds < notification.fade_in_duration) {
            notification.target_alpha = elapsed_seconds / notification.fade_in_duration;
        } else {
            notification.target_alpha = 1.0f;
        }
    }

    // Smooth alpha transition
    float alpha_speed = 5.0f * animation_speed_;
    float alpha_diff = notification.target_alpha - notification.alpha;
    notification.alpha += alpha_diff * alpha_speed * ImGui::GetIO().DeltaTime;

    // Clamp alpha
    notification.alpha = std::max(0.0f, std::min(1.0f, notification.alpha));
}

float NotificationSystem::CalculateNotificationAlpha(const Notification& notification) const {
    return notification.alpha;
}

void NotificationSystem::RenderNotification(const Notification& notification, float y_offset) {
    ImVec2 pos = CalculateNotificationPosition(y_offset);

    // Set up window flags for notification - removed NoInputs to ensure visibility
    ImGuiWindowFlags window_flags = ImGuiWindowFlags_NoDecoration |
                                   ImGuiWindowFlags_NoMove |
                                   ImGuiWindowFlags_NoResize |
                                   ImGuiWindowFlags_NoSavedSettings |
                                   ImGuiWindowFlags_NoFocusOnAppearing |
                                   ImGuiWindowFlags_NoNav |
                                   ImGuiWindowFlags_AlwaysAutoResize;

    // Apply alpha to colors
    float alpha = CalculateNotificationAlpha(notification);
    ImVec4 bg_color = GetTypeColor(notification.type);
    bg_color.w *= alpha;

    ImVec4 border_color = GetTypeBorderColor(notification.type);
    border_color.w *= alpha;

    // Set window background color
    ImGui::PushStyleColor(ImGuiCol_WindowBg, bg_color);
    ImGui::PushStyleColor(ImGuiCol_Border, border_color);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 2.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 8.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(12.0f, 10.0f));

    // Create unique window name
    std::string window_name = "Notification##" + std::to_string(reinterpret_cast<uintptr_t>(&notification));

    ImGui::SetNextWindowPos(pos, ImGuiCond_Always);
    ImGui::SetNextWindowSize(ImVec2(notification_width_, 0), ImGuiCond_Always); // Auto height

    printf("Rendering notification window at pos (%.1f, %.1f) with alpha %.3f\n", pos.x, pos.y, alpha);

    if (ImGui::Begin(window_name.c_str(), nullptr, window_flags)) {
        // Icon and message
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 1.0f, 1.0f, alpha));

        // Draw icon
        ImGui::Text("%s", GetTypeIcon(notification.type));
        ImGui::SameLine();

        // Draw message with text wrapping
        ImGui::PushTextWrapPos(ImGui::GetContentRegionAvail().x);
        ImGui::Text("%s", notification.message.c_str());
        ImGui::PopTextWrapPos();

        ImGui::PopStyleColor(); // Text color

        printf("Notification content rendered: %s\n", notification.message.c_str());
    }
    ImGui::End();

    ImGui::PopStyleVar(3); // WindowPadding, WindowRounding, WindowBorderSize
    ImGui::PopStyleColor(2); // WindowBg, Border
}

ImVec2 NotificationSystem::CalculateNotificationPosition(float y_offset) const {
    return ImVec2(position_.x, position_.y + y_offset);
}

void NotificationSystem::RemoveExpiredNotifications() {
    notifications_.erase(
        std::remove_if(notifications_.begin(), notifications_.end(),
            [](const std::unique_ptr<Notification>& n) {
                if (n->is_persistent) return false;

                auto now = std::chrono::steady_clock::now();
                auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - n->creation_time).count();
                float elapsed_seconds = elapsed / 1000.0f;

                return elapsed_seconds >= n->duration_seconds && n->alpha <= 0.01f;
            }),
        notifications_.end()
    );
}

void NotificationSystem::EnsureMaxNotificationLimit() {
    while (notifications_.size() > max_notifications_) {
        notifications_.erase(notifications_.begin());
    }
}

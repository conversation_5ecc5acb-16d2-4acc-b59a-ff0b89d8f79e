#include "notification_system.h"
#include <algorithm>
#include <cmath>
#include <iostream>

NotificationSystem::NotificationSystem()
    : position_(Vec2(0.0f, 30.0f))  // Top-right, below menu bar
    , notification_width_(350.0f)   // Wider for better visibility
    , notification_height_(80.0f)   // Taller for better visibility
    , notification_spacing_(15.0f)
    , max_notifications_(5)
    , default_duration_(5.0f)       // Longer duration for testing
    , animation_speed_(1.0f)
    , window_width_(1280)
    , window_height_(720)
{
}

NotificationSystem::~NotificationSystem() {
}

void NotificationSystem::Update() {
    // Update animations for all notifications
    for (auto& notification : notifications_) {
        UpdateNotificationAnimation(*notification);
    }
    
    // Remove expired notifications
    RemoveExpiredNotifications();
    
    // Ensure we don't exceed max notification limit
    EnsureMaxNotificationLimit();
}

void NotificationSystem::Render(OpenGLRenderer& renderer) {
    if (notifications_.empty()) {
        return;
    }

    // Update position to be at top-right
    position_.x = window_width_ - notification_width_ - 20.0f;  // 20px margin from right edge

    float current_y_offset = 0.0f;

    // Render notifications from top to bottom
    for (size_t i = 0; i < notifications_.size(); ++i) {
        const auto& notification = notifications_[i];

        if (notification->alpha > 0.01f) {  // Only render visible notifications
            RenderNotification(renderer, *notification, current_y_offset);
            current_y_offset += notification_height_ + notification_spacing_;
        }
    }
}

void NotificationSystem::AddNotification(const std::string& message, NotificationType type, float duration) {
    auto notification = std::make_unique<Notification>(message, type, duration, false);
    notifications_.push_back(std::move(notification));

    // Debug output
    printf("Added notification: %s (count: %zu)\n", message.c_str(), notifications_.size());
}

void NotificationSystem::AddPersistentNotification(const std::string& message, NotificationType type) {
    auto notification = std::make_unique<Notification>(message, type, 0.0f, true);
    notifications_.push_back(std::move(notification));
}

void NotificationSystem::ShowInfo(const std::string& message, float duration) {
    AddNotification(message, NotificationType::INFO, duration);
}

void NotificationSystem::ShowSuccess(const std::string& message, float duration) {
    AddNotification(message, NotificationType::SUCCESS, duration);
}

void NotificationSystem::ShowWarning(const std::string& message, float duration) {
    AddNotification(message, NotificationType::WARNING, duration);
}

void NotificationSystem::ShowError(const std::string& message, float duration) {
    AddNotification(message, NotificationType::ERROR, duration);
}

void NotificationSystem::ClearAll() {
    notifications_.clear();
}

void NotificationSystem::ClearType(NotificationType type) {
    notifications_.erase(
        std::remove_if(notifications_.begin(), notifications_.end(),
            [type](const std::unique_ptr<Notification>& n) {
                return n->type == type;
            }),
        notifications_.end()
    );
}

void NotificationSystem::RemoveNotification(size_t index) {
    if (index < notifications_.size()) {
        notifications_.erase(notifications_.begin() + index);
    }
}

void NotificationSystem::SetPosition(float x, float y) {
    position_ = Vec2(x, y);
}

void NotificationSystem::SetMaxNotifications(size_t max_count) {
    max_notifications_ = max_count;
}

void NotificationSystem::SetDefaultDuration(float duration) {
    default_duration_ = duration;
}

void NotificationSystem::SetAnimationSpeed(float speed) {
    animation_speed_ = speed;
}

void NotificationSystem::SetWindowSize(int width, int height) {
    window_width_ = width;
    window_height_ = height;
}

size_t NotificationSystem::GetNotificationCount() const {
    return notifications_.size();
}

bool NotificationSystem::HasNotifications() const {
    return !notifications_.empty();
}

bool NotificationSystem::HasNotificationsOfType(NotificationType type) const {
    return std::any_of(notifications_.begin(), notifications_.end(),
        [type](const std::unique_ptr<Notification>& n) {
            return n->type == type;
        });
}

Color NotificationSystem::GetTypeColor(NotificationType type) const {
    switch (type) {
        case NotificationType::INFO:
            return Color(0.1f, 0.4f, 0.9f, 0.95f);  // Bright Blue
        case NotificationType::SUCCESS:
            return Color(0.1f, 0.7f, 0.1f, 0.95f);  // Bright Green
        case NotificationType::WARNING:
            return Color(0.9f, 0.6f, 0.1f, 0.95f);  // Bright Orange
        case NotificationType::ERROR:
            return Color(0.9f, 0.1f, 0.1f, 0.95f);  // Bright Red
        default:
            return Color(0.3f, 0.3f, 0.3f, 0.95f);  // Dark Gray
    }
}

Color NotificationSystem::GetTypeBorderColor(NotificationType type) const {
    Color base_color = GetTypeColor(type);
    return Color(base_color.r * 0.8f, base_color.g * 0.8f, base_color.b * 0.8f, base_color.a);
}

const char* NotificationSystem::GetTypeIcon(NotificationType type) const {
    switch (type) {
        case NotificationType::INFO:
            return "ℹ";
        case NotificationType::SUCCESS:
            return "✓";
        case NotificationType::WARNING:
            return "⚠";
        case NotificationType::ERROR:
            return "✗";
        default:
            return "•";
    }
}

void NotificationSystem::UpdateNotificationAnimation(Notification& notification) {
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - notification.creation_time).count();
    float elapsed_seconds = elapsed / 1000.0f;

    // Debug output for first notification only
    static bool first_debug = true;
    if (first_debug && elapsed_seconds < 0.1f) {
        printf("First notification animation: elapsed=%.3fs, alpha=%.3f, target_alpha=%.3f\n",
               elapsed_seconds, notification.alpha, notification.target_alpha);
        first_debug = false;
    }

    // Check if notification should start fading out
    if (!notification.is_persistent && !notification.is_fading_out &&
        elapsed_seconds >= (notification.duration_seconds - notification.fade_out_duration)) {
        notification.is_fading_out = true;
        notification.target_alpha = 0.0f;
    }

    // Calculate target alpha based on animation phase
    if (!notification.is_fading_out) {
        // Fade in phase
        if (elapsed_seconds < notification.fade_in_duration) {
            notification.target_alpha = elapsed_seconds / notification.fade_in_duration;
        } else {
            notification.target_alpha = 1.0f;
        }
    }

    // Smooth alpha transition
    float alpha_speed = 5.0f * animation_speed_;
    float alpha_diff = notification.target_alpha - notification.alpha;
    float delta_time = 1.0f / 60.0f; // Assume 60 FPS for now
    notification.alpha += alpha_diff * alpha_speed * delta_time;

    // Clamp alpha
    notification.alpha = std::max(0.0f, std::min(1.0f, notification.alpha));
}

float NotificationSystem::CalculateNotificationAlpha(const Notification& notification) const {
    return notification.alpha;
}

void NotificationSystem::RenderNotification(OpenGLRenderer& renderer, const Notification& notification, float y_offset) {
    Vec2 pos = CalculateNotificationPosition(y_offset);

    // Apply alpha to colors
    float alpha = CalculateNotificationAlpha(notification);
    Color bg_color = GetTypeColor(notification.type);
    bg_color.a *= alpha;

    Color border_color = GetTypeBorderColor(notification.type);
    border_color.a *= alpha;

    // Draw notification background with rounded border
    renderer.DrawRectWithRoundedBorder(
        pos,
        Vec2(notification_width_, notification_height_),
        bg_color,
        border_color,
        3.0f,  // Thicker border for visibility
        12.0f  // Larger corner radius
    );

    // Draw icon and text
    Color text_color = Color(1.0f, 1.0f, 1.0f, alpha);  // White text with alpha

    // Draw icon at left side (centered vertically)
    Vec2 icon_pos = Vec2(pos.x + 20.0f, pos.y + notification_height_ * 0.5f);
    std::string icon = GetTypeIcon(notification.type);
    renderer.DrawTextCentered(icon_pos, icon, text_color, 20.0f);

    // Draw message text (left-aligned, centered vertically)
    Vec2 text_pos = Vec2(pos.x + 50.0f, pos.y + notification_height_ * 0.5f - 8.0f);  // Offset for better centering
    renderer.DrawText(text_pos, notification.message, text_color, 16.0f);
}

Vec2 NotificationSystem::CalculateNotificationPosition(float y_offset) const {
    return Vec2(position_.x, position_.y + y_offset);
}

void NotificationSystem::RemoveExpiredNotifications() {
    notifications_.erase(
        std::remove_if(notifications_.begin(), notifications_.end(),
            [](const std::unique_ptr<Notification>& n) {
                if (n->is_persistent) return false;

                auto now = std::chrono::steady_clock::now();
                auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - n->creation_time).count();
                float elapsed_seconds = elapsed / 1000.0f;

                return elapsed_seconds >= n->duration_seconds && n->alpha <= 0.01f;
            }),
        notifications_.end()
    );
}

void NotificationSystem::EnsureMaxNotificationLimit() {
    while (notifications_.size() > max_notifications_) {
        notifications_.erase(notifications_.begin());
    }
}
